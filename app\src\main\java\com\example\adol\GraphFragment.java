package com.example.adol;

import android.os.Bundle;
import android.os.Environment;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.v4.app.Fragment;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.github.mikephil.charting.charts.LineChart;
import com.github.mikephil.charting.components.Description;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.data.LineData;
import com.github.mikephil.charting.data.LineDataSet;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.example.adol.MainActivity.semaphore;


/**
 * A simple {@link Fragment} subclass.
 */
public class GraphFragment extends Fragment {

    private static final String TAG = "Graph";

    private LineChart chart;

    private boolean exitThread;

    private int lineNumber;
    private float[] avgCurrent = new float[7];
    private int indexCurrent;
    private List<Entry> entries = new ArrayList<>();
    private long time;


    public GraphFragment() {
        // Required empty public constructor
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View view = inflater.inflate(R.layout.fragment_graph, container, false);
        chart = view.findViewById(R.id.chart);
        Description description = chart.getDescription();
        description.setText("X-Axis in sec    Y-Axis in nA");
        description.setTextSize(14f);
        return view;
    }

    /**
     * Starts a thread that updates the UI for a live graph
     */

    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        //you can set the title for your toolbar here for different fragments different titles
        Objects.requireNonNull(getActivity()).setTitle("Graph");

        Thread thread = new Thread() {
            @Override
            public void run() {
                try {
                    while(!exitThread) {
                        Objects.requireNonNull(getActivity()).runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                adding_Data();
                            }
                        });
                        if(time<3600000){ // When measuring time is over 1h the display update rate is reduced to every 3s for performance enhancement
                            Thread.sleep(1000);
                        }else{
                            Thread.sleep(3000);
                        }

                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        };
        exitThread = false;
        thread.start();
    }

    /**
     * Reads the data from the File and shows theme in the graph.
     *
     * For better performance the read lines get skipped and the data get stored in a class wide List
     * The x (time) data is the time difference between the first element of the file and the actual element.
     * The y (current) values are averaged over 8 values.
     */

    private void adding_Data (){
        File SensorDatafile = new File(Environment.getExternalStorageDirectory() + "/Sensor_Data/sensor_data.csv");
        if(SensorDatafile.exists()){
            try{
                semaphore.acquire();
                BufferedReader br = new BufferedReader(new FileReader(SensorDatafile));
                try {
                    float current;
                    //long time;
                    br.readLine();
                    String line = br.readLine();
                    String[] parts = line.split(";");
                    long offsetTime = Long.parseLong(parts[1]);
                    for(int i=0;i<lineNumber;i++){
                        line = br.readLine();
                        indexCurrent= (indexCurrent+1) % 8;
                    }

                    while (line != null) {
                        parts = line.split(";");
                        current = Float.parseFloat(parts[2]);
                        if(indexCurrent == 7){
                            time = Long.parseLong(parts[1])-offsetTime;
                            for(int a=0;a<7;a++){
                                current = avgCurrent[a] + current;
                            }
                            current = current/8;

                            entries.add(new Entry((float)time/1000,current));
                            indexCurrent = 0;
                        }else{
                            avgCurrent[indexCurrent] = current;
                            indexCurrent++;
                        }
                        lineNumber++;
                        line = br.readLine();
                    }
                }catch (Exception e){
                    e.printStackTrace();
                } finally {
                    try {
                        br.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }catch (FileNotFoundException e){
                Log.e(TAG, "File not found");
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
               semaphore.release();
            }


            LineDataSet dataSet = new LineDataSet(entries, "Current"); // add entries to dataset
            LineData lineData = new LineData(dataSet);
            chart.setData(lineData);
            chart.invalidate(); // refresh
        }
    }

    @Override
    public void onDetach() {
        super.onDetach();
        exitThread = true;
    }

    public void resetDataInGraph(){
        lineNumber=0;
        entries.clear();
    }
}
