package com.example.adol;


class ReceivedValues {
    int ledNr;
    int ADCon;
    int ADCoff;
    int integrationtime;
    int timeOffset;
    int ADCBat;
    int ADCRef;

    ReceivedValues(byte[] txValue){
        ledNr = toUnsignedInt(txValue[0]);
        ADCon = (toUnsignedInt(txValue[1])<<8)+toUnsignedInt(txValue[2]);
        ADCoff = (toUnsignedInt(txValue[3])<<8)+toUnsignedInt(txValue[4]);
        integrationtime = (toUnsignedInt(txValue[5])<<8)+toUnsignedInt(txValue[6]);
        timeOffset = (toUnsignedInt(txValue[7])<<8)+toUnsignedInt(txValue[8]);
        ADCBat = (toUnsignedInt(txValue[9])<<8)+toUnsignedInt(txValue[10]);
        ADCRef = (toUnsignedInt(txValue[11])<<8)+toUnsignedInt(txValue[12]);
    }


    /**
     * Convert a unsigned byte to a signed int
     *
     * @param b as a Unsigned Byte
     * @return Unsigned Int
     */

    private int toUnsignedInt (byte b){
        return ((int) b) & 0xFF;
    }

}
