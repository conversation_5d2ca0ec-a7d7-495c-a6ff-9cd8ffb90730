package com.example.adol;


/**
 * Class for storing and calculating the Hardware Configuration.
 */
class ConfigHardware {

    static int TIA_Integrator = 0;                  // 0 TIA, 1 Integrator
    static int gain_TIA = 20000;                    // Gain/1000
    static double Vref_ADC = 0.6;                   // in V
    static double gain_ADC_Photo = (double)1/2;     // Gain
    static double gain_ADC_Bat = (double) 1/6;
    static double gain_ADC_ref= (double)1/2;
    static int num_of_Bit_ADC = 14;
    static int capacity_con = 10;                   // in pF
    static double divider_Integrator = (double)10/(47+10);
    static double divider_Bat = 0.5;

    // For better performance and usability, value get calculated only once
    static double factor_current;
    static double factor_Bat;
    static double factor_Vref;

    /**
     * Converts and merges the Byte Array to usable numbers
     *
     * @param txValue received Byte Array from the sensor
     */
    ConfigHardware(byte[] txValue){
        TIA_Integrator      = toUnsignedInt(txValue[1]);
        gain_TIA            = (toUnsignedInt(txValue[2]) << 8) + toUnsignedInt(txValue[3]);
        Vref_ADC            = (double) toUnsignedInt(txValue[4])/ 100;
        gain_ADC_Photo      = (double) toUnsignedInt(txValue[5]) / 6;
        gain_ADC_Bat        = (double) toUnsignedInt(txValue[6]) / 6;
        gain_ADC_ref        = (double) toUnsignedInt(txValue[7]) / 6;
        num_of_Bit_ADC      = toUnsignedInt(txValue[8]);
        capacity_con        = toUnsignedInt(txValue[9]);
        divider_Integrator  = (double) toUnsignedInt(txValue[10]) / (double)(toUnsignedInt(txValue[10]) + toUnsignedInt(txValue[11]));
        divider_Bat         = (double) toUnsignedInt(txValue[12]) / (double)(toUnsignedInt(txValue[12]) + toUnsignedInt(txValue[13]));
        factor_current      = Vref_ADC / (gain_ADC_Photo * Math.pow(2,num_of_Bit_ADC));
        factor_Bat          = Vref_ADC / (Math.pow(2,num_of_Bit_ADC) * gain_ADC_Bat*divider_Bat);
        factor_Vref         = Vref_ADC / (Math.pow(2,num_of_Bit_ADC) * gain_ADC_ref);
    }


    /**
     * Convert a unsigned byte to a signed int
     *
     * @param b as a Unsigned Byte
     * @return Unsigned Int
     */
    private int toUnsignedInt (byte b){
        return ((int) b) & 0xFF;
    }


}
