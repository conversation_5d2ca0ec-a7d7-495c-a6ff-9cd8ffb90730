package com.example.adol;

import android.content.Context;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.v4.app.Fragment;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;

import java.util.Objects;

import static com.example.adol.MainActivity.PROFILE_CONNECTED;


/**
 * A simple {@link Fragment} subclass.
 * Activities that contain this fragment must implement the
 * {@link DebuggingFragment.OnFragmentInteractionListener} interface
 * to handle interaction events.
 */
public class DebuggingFragment extends Fragment {


    private Button button_send;
    @SuppressWarnings("FieldCanBeLocal")
    private Button button_saveFile;
    private EditText text_command;
    private TextView textView_value_current;
    private TextView textView_value_SNR;
    private TextView textView_value_stdev;
    private TextView textView_value_LEDNr;
    private TextView textView_value_mesTime;
    private TextView textView_value_ADCon;
    private TextView textView_value_ADCoff;
    private TextView textView_value_Timeoffset;
    private TextView textView_value_ADCbatt;
    private TextView textView_value_ADCref;
    @SuppressWarnings("FieldCanBeLocal")
    private TextView textView_value_pathFile;


    private OnFragmentInteractionListener mListener;

    public DebuggingFragment() {
        // Required empty public constructor
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View view = inflater.inflate(R.layout.fragment_debugging, container, false);
        button_send = view.findViewById(R.id.button_send);
        button_saveFile = view.findViewById(R.id.button_saveFile);
        text_command = view.findViewById(R.id.text_command);
        textView_value_current = view.findViewById(R.id.textView_value_current);
        textView_value_SNR = view.findViewById(R.id.textView_value_SNR);
        textView_value_stdev = view.findViewById(R.id.textView_value_stdev);
        textView_value_LEDNr = view.findViewById(R.id.textView_value_LEDNr);
        textView_value_mesTime = view.findViewById(R.id.textView_value_mesTime);
        textView_value_ADCon = view.findViewById(R.id.textView_value_ADCon);
        textView_value_ADCoff = view.findViewById(R.id.textView_value_ADCoff);
        textView_value_Timeoffset = view.findViewById(R.id.textView_value_Timeoffset);
        textView_value_ADCbatt = view.findViewById(R.id.textView_value_ADCbatt);
        textView_value_ADCref = view.findViewById(R.id.textView_value_ADCref);
        textView_value_pathFile = view.findViewById(R.id.textView_value_pathFile);

        // Set the initial State depending on the current connection
        if (((MainActivity) Objects.requireNonNull(getActivity())).get_mState_connection() == PROFILE_CONNECTED) {
            button_send.setEnabled(true);
            text_command.setEnabled(true);
        } else {
            button_send.setEnabled(false);
            text_command.setEnabled(false);
        }
        textView_value_pathFile.setText(((MainActivity) getActivity()).get_FilePath());

        button_send.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                byte[] command = text_command.getText().toString().getBytes();
                sendBack(command);
            }
        });

        button_saveFile.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mListener != null) {
                    mListener.onFragmentInteraction(null, R.id.button_saveFile);
                }
            }
        });
        return view;
    }

    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        Objects.requireNonNull(getActivity()).setTitle("Debugging");
    }


    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        if (context instanceof OnFragmentInteractionListener) {
            mListener = (OnFragmentInteractionListener) context;
        } else {
            throw new RuntimeException(context.toString()
                    + " must implement OnFragmentInteractionListener");
        }
    }

    @Override
    public void onDetach() {
        super.onDetach();
        mListener = null;
    }

    /**
     * Sends the command back to the {@link MainActivity}
     * @param command byte array with the text
     */

    private void sendBack(byte[] command) {
        if (mListener != null) {
            mListener.onFragmentInteraction(command, 0);
        }
    }

    /**
     * Change te text of e specific item
     *
     * @param text text to display
     * @param id XML id of the target item
     */
    public void changeText(String text, int id) {
        switch (id) {
            case R.id.button_send:
                button_send.setText(text);
                break;
            case R.id.textView_value_current:
                textView_value_current.setText(text);
                break;
            case R.id.textView_value_SNR:
                textView_value_SNR.setText(text);
                break;
            case R.id.textView_value_stdev:
                textView_value_stdev.setText(text);
                break;
            case R.id.textView_value_LEDNr:
                textView_value_LEDNr.setText(text);
                break;
            case R.id.textView_value_mesTime:
                textView_value_mesTime.setText(text);
                break;
            case R.id.textView_value_ADCon:
                textView_value_ADCon.setText(text);
                break;
            case R.id.textView_value_ADCoff:
                textView_value_ADCoff.setText(text);
                break;
            case R.id.textView_value_Timeoffset:
                textView_value_Timeoffset.setText(text);
                break;
            case R.id.textView_value_ADCbatt:
                textView_value_ADCbatt.setText(text);
                break;
            case R.id.textView_value_ADCref:
                textView_value_ADCref.setText(text);
                break;
        }
    }

    /**
     * Change status of a item to enable or disable
     *
     * @param true_false true enable, false disable
     * @param id XML id of the target item
     */
    public void setEnabledDisable(boolean true_false, int id) {
        switch (id) {
            case R.id.button_send:
                button_send.setEnabled(true_false);
                break;
            case R.id.text_command:
                text_command.setEnabled(true_false);
                break;
        }
    }

    public interface OnFragmentInteractionListener {
        void onFragmentInteraction(byte[] command, int id);
    }
}
