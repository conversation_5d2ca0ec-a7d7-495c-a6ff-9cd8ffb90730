<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:context=".HardwareConfigFragment">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/textView_TIAIntegrator"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_marginTop="10dp"
            android:text="@string/tiaintegrator"
            android:textAppearance="?android:attr/textAppearanceMedium" />

        <TextView
            android:id="@+id/textView_value_TIAIntegrator"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@+id/textView_TIAIntegrator"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="10dp"
            android:layout_toEndOf="@id/textView_TIAIntegrator"
            android:gravity="end"
            android:text=""
            android:textAppearance="?android:attr/textAppearanceMedium" />


        <TextView
            android:id="@+id/textView_gain"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/textView_TIAIntegrator"
            android:layout_alignStart="@id/textView_TIAIntegrator"
            android:layout_marginTop="10dp"
            android:text="@string/gain_tia"
            android:textAppearance="?android:attr/textAppearanceMedium" />

        <TextView
            android:id="@+id/textView_value_gain"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@+id/textView_gain"
            android:layout_alignEnd="@+id/textView_value_TIAIntegrator"
            android:layout_toEndOf="@id/textView_gain"
            android:gravity="end"
            android:text=""
            android:textAppearance="?android:attr/textAppearanceMedium" />


        <TextView
            android:id="@+id/textView_VrefADC"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/textView_gain"
            android:layout_alignStart="@id/textView_TIAIntegrator"
            android:layout_marginTop="10dp"
            android:text="@string/vref_adc"
            android:textAppearance="?android:attr/textAppearanceMedium" />

        <TextView
            android:id="@+id/textView_value_VrefADC"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@+id/textView_VrefADC"
            android:layout_alignEnd="@+id/textView_value_TIAIntegrator"
            android:layout_toEndOf="@id/textView_VrefADC"
            android:gravity="end"
            android:text=""
            android:textAppearance="?android:attr/textAppearanceMedium" />



        <TextView
            android:id="@+id/textView_GainADC_Photo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/textView_VrefADC"
            android:layout_alignStart="@id/textView_TIAIntegrator"
            android:layout_marginTop="10dp"
            android:text="@string/gain_adc_photo"
            android:textAppearance="?android:attr/textAppearanceMedium" />

        <TextView
            android:id="@+id/textView_value_GainADC_Photo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@+id/textView_GainADC_Photo"
            android:layout_alignEnd="@+id/textView_value_TIAIntegrator"
            android:layout_toEndOf="@id/textView_GainADC_Photo"
            android:gravity="end"
            android:text=""
            android:textAppearance="?android:attr/textAppearanceMedium" />

        <TextView
            android:id="@+id/textView_GainADC_Bat"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/textView_GainADC_Photo"
            android:layout_alignStart="@id/textView_TIAIntegrator"
            android:layout_marginTop="10dp"
            android:text="@string/gain_adc_battery"
            android:textAppearance="?android:attr/textAppearanceMedium" />

        <TextView
            android:id="@+id/textView_value_GainADC_Bat"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@+id/textView_GainADC_Bat"
            android:layout_alignEnd="@+id/textView_value_TIAIntegrator"
            android:layout_toEndOf="@id/textView_GainADC_Bat"
            android:gravity="end"
            android:text=""
            android:textAppearance="?android:attr/textAppearanceMedium" />

        <TextView
            android:id="@+id/textView_GainADC_ref"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/textView_GainADC_Bat"
            android:layout_alignStart="@id/textView_TIAIntegrator"
            android:layout_marginTop="10dp"
            android:text="@string/gain_adc_reference"
            android:textAppearance="?android:attr/textAppearanceMedium" />

        <TextView
            android:id="@+id/textView_value_GainADC_ref"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@+id/textView_GainADC_ref"
            android:layout_alignEnd="@+id/textView_value_TIAIntegrator"
            android:layout_toEndOf="@id/textView_GainADC_ref"
            android:gravity="end"
            android:text=""
            android:textAppearance="?android:attr/textAppearanceMedium" />


        <TextView
            android:id="@+id/textView_numOfBit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/textView_GainADC_ref"
            android:layout_alignStart="@id/textView_TIAIntegrator"
            android:layout_marginTop="10dp"
            android:text="@string/resolution"
            android:textAppearance="?android:attr/textAppearanceMedium" />

        <TextView
            android:id="@+id/textView_value_numOfBit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@+id/textView_numOfBit"
            android:layout_alignEnd="@+id/textView_value_TIAIntegrator"
            android:layout_toEndOf="@id/textView_numOfBit"
            android:gravity="end"
            android:text=""
            android:textAppearance="?android:attr/textAppearanceMedium" />


        <TextView
            android:id="@+id/textView_capInt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/textView_numOfBit"
            android:layout_alignStart="@id/textView_TIAIntegrator"
            android:layout_marginTop="10dp"
            android:text="@string/integratorcapacity"
            android:textAppearance="?android:attr/textAppearanceMedium" />

        <TextView
            android:id="@+id/textView_value_capInt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@+id/textView_capInt"
            android:layout_alignEnd="@+id/textView_value_TIAIntegrator"
            android:layout_toEndOf="@id/textView_capInt"
            android:gravity="end"
            android:text=""
            android:textAppearance="?android:attr/textAppearanceMedium" />


        <TextView
            android:id="@+id/textView_dividerIntegrator"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/textView_capInt"
            android:layout_alignStart="@id/textView_TIAIntegrator"
            android:layout_marginTop="10dp"
            android:text="@string/divider_integrator"
            android:textAppearance="?android:attr/textAppearanceMedium" />

        <TextView
            android:id="@+id/textView_value_dividerIntegrator"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@+id/textView_dividerIntegrator"
            android:layout_alignEnd="@+id/textView_value_TIAIntegrator"
            android:layout_toEndOf="@id/textView_dividerIntegrator"
            android:gravity="end"
            android:text=""
            android:textAppearance="?android:attr/textAppearanceMedium" />


        <TextView
            android:id="@+id/textView_dividerBat"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/textView_dividerIntegrator"
            android:layout_alignStart="@id/textView_TIAIntegrator"
            android:layout_marginTop="10dp"
            android:text="@string/divider_battery"
            android:textAppearance="?android:attr/textAppearanceMedium" />

        <TextView
            android:id="@+id/textView_value_dividerBat"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@+id/textView_dividerBat"
            android:layout_alignEnd="@+id/textView_value_TIAIntegrator"
            android:layout_toEndOf="@id/textView_dividerBat"
            android:gravity="end"
            android:text=""
            android:textAppearance="?android:attr/textAppearanceMedium" />

    </RelativeLayout>
</ScrollView>
