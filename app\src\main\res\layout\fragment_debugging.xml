<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".DebuggingFragment">

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/textView_current"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="10dp"
                    android:text="@string/current"
                    android:textAppearance="?android:attr/textAppearanceMedium" />

                <TextView
                    android:id="@+id/textView_value_current"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@+id/textView_current"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="10dp"
                    android:layout_toEndOf="@id/textView_current"
                    android:gravity="end"
                    android:text=""
                    android:textAppearance="?android:attr/textAppearanceMedium" />


                <TextView
                    android:id="@+id/textView_SNR"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/textView_current"
                    android:layout_alignStart="@id/textView_current"
                    android:layout_marginTop="10dp"
                    android:text="@string/snr"
                    android:textAppearance="?android:attr/textAppearanceMedium" />

                <TextView
                    android:id="@+id/textView_value_SNR"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@+id/textView_SNR"
                    android:layout_alignEnd="@+id/textView_value_current"
                    android:layout_toEndOf="@id/textView_SNR"
                    android:gravity="end"
                    android:text=""
                    android:textAppearance="?android:attr/textAppearanceMedium" />


                <TextView
                    android:id="@+id/textView_stdev"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/textView_SNR"
                    android:layout_alignStart="@id/textView_current"
                    android:layout_marginTop="10dp"
                    android:text="@string/stdev"
                    android:textAppearance="?android:attr/textAppearanceMedium" />

                <TextView
                    android:id="@+id/textView_value_stdev"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@+id/textView_stdev"
                    android:layout_alignEnd="@+id/textView_value_current"
                    android:layout_toEndOf="@id/textView_stdev"
                    android:gravity="end"
                    android:text=""
                    android:textAppearance="?android:attr/textAppearanceMedium" />

                <View
                    android:id="@+id/divider"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_below="@+id/textView_stdev"
                    android:layout_marginTop="10dp"
                    android:background="?android:attr/listDivider" />


                <TextView
                    android:id="@+id/textView_LEDNr"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/divider"
                    android:layout_alignStart="@id/textView_current"
                    android:layout_marginTop="10dp"
                    android:text="@string/led_nr"
                    android:textAppearance="?android:attr/textAppearanceMedium" />

                <TextView
                    android:id="@+id/textView_value_LEDNr"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@+id/textView_LEDNr"
                    android:layout_alignEnd="@+id/textView_value_current"
                    android:layout_toEndOf="@id/textView_LEDNr"
                    android:gravity="end"
                    android:text=""
                    android:textAppearance="?android:attr/textAppearanceMedium" />


                <TextView
                    android:id="@+id/textView_mesTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/textView_LEDNr"
                    android:layout_alignStart="@id/textView_current"
                    android:layout_marginTop="10dp"
                    android:text="@string/measuring_time"
                    android:textAppearance="?android:attr/textAppearanceMedium" />

                <TextView
                    android:id="@+id/textView_value_mesTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@+id/textView_mesTime"
                    android:layout_alignEnd="@+id/textView_value_current"
                    android:layout_toEndOf="@id/textView_mesTime"
                    android:gravity="end"
                    android:text=""
                    android:textAppearance="?android:attr/textAppearanceMedium" />


                <TextView
                    android:id="@+id/textView_ADCon"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/textView_mesTime"
                    android:layout_alignStart="@id/textView_current"
                    android:layout_marginTop="10dp"
                    android:text="@string/current_on"
                    android:textAppearance="?android:attr/textAppearanceMedium" />

                <TextView
                    android:id="@+id/textView_value_ADCon"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@+id/textView_ADCon"
                    android:layout_alignEnd="@+id/textView_value_current"
                    android:layout_toEndOf="@id/textView_ADCon"
                    android:gravity="end"
                    android:text=""
                    android:textAppearance="?android:attr/textAppearanceMedium" />


                <TextView
                    android:id="@+id/textView_ADCoff"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/textView_ADCon"
                    android:layout_alignStart="@id/textView_current"
                    android:layout_marginTop="10dp"
                    android:text="@string/current_off"
                    android:textAppearance="?android:attr/textAppearanceMedium" />

                <TextView
                    android:id="@+id/textView_value_ADCoff"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@+id/textView_ADCoff"
                    android:layout_alignEnd="@+id/textView_value_current"
                    android:layout_toEndOf="@id/textView_ADCoff"
                    android:gravity="end"
                    android:text=""
                    android:textAppearance="?android:attr/textAppearanceMedium" />


                <TextView
                    android:id="@+id/textView_Timeoffset"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/textView_ADCoff"
                    android:layout_alignStart="@id/textView_current"
                    android:layout_marginTop="10dp"
                    android:text="@string/time_offset"
                    android:textAppearance="?android:attr/textAppearanceMedium" />

                <TextView
                    android:id="@+id/textView_value_Timeoffset"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@+id/textView_Timeoffset"
                    android:layout_alignEnd="@+id/textView_value_current"
                    android:layout_toEndOf="@id/textView_Timeoffset"
                    android:gravity="end"
                    android:text=""
                    android:textAppearance="?android:attr/textAppearanceMedium" />


                <TextView
                    android:id="@+id/textView_ADCbatt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/textView_Timeoffset"
                    android:layout_alignStart="@id/textView_current"
                    android:layout_marginTop="10dp"
                    android:text="@string/v_battery"
                    android:textAppearance="?android:attr/textAppearanceMedium" />

                <TextView
                    android:id="@+id/textView_value_ADCbatt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@+id/textView_ADCbatt"
                    android:layout_alignEnd="@+id/textView_value_current"
                    android:layout_toEndOf="@id/textView_ADCbatt"
                    android:gravity="end"
                    android:text=""
                    android:textAppearance="?android:attr/textAppearanceMedium" />


                <TextView
                    android:id="@+id/textView_ADCref"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/textView_ADCbatt"
                    android:layout_alignStart="@id/textView_current"
                    android:layout_marginTop="10dp"
                    android:text="@string/v_reference"
                    android:textAppearance="?android:attr/textAppearanceMedium" />

                <TextView
                    android:id="@+id/textView_value_ADCref"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@+id/textView_ADCref"
                    android:layout_alignEnd="@+id/textView_value_current"
                    android:layout_toEndOf="@id/textView_ADCref"
                    android:gravity="end"
                    android:text=""
                    android:textAppearance="?android:attr/textAppearanceMedium" />


                <View
                    android:id="@+id/divider2"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_below="@+id/textView_ADCref"
                    android:layout_marginTop="10dp"
                    android:background="?android:attr/listDivider" />

                <TextView
                    android:id="@+id/textView_pathFile"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/divider2"
                    android:layout_alignStart="@id/textView_current"
                    android:layout_marginTop="10dp"
                    android:text="@string/path_of_file"
                    android:textAppearance="?android:attr/textAppearanceMedium" />

                <TextView
                    android:id="@+id/textView_value_pathFile"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@+id/textView_pathFile"
                    android:layout_alignEnd="@+id/textView_value_current"
                    android:layout_toEndOf="@id/textView_pathFile"
                    android:gravity="end"
                    android:text=""
                    android:textAppearance="?android:attr/textAppearanceMedium" />

                <Button
                    android:id="@+id/button_saveFile"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/textView_value_pathFile"
                    android:layout_marginTop="10dp"
                    android:text="@string/save_file" />

            </RelativeLayout>
        </ScrollView>

    <!-- TODO: Höhe des RelativeLayout dynamisch Einstellen (Problem wenn auf wrap_contetn wird ganzer Bildschrim gefüllt -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_weight="0">

        <EditText
            android:id="@+id/text_command"
            android:layout_width="102dp"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_alignParentBottom="true"
            android:layout_gravity="bottom"
            android:layout_toStartOf="@+id/button_send"
            android:ems="10"
            android:enabled="false"
            android:fontFamily="1"
            android:gravity="bottom"
            android:inputType="text"
            android:lines="1"
            android:maxLength="20"
            android:maxLines="1"
            android:shadowRadius="1"
            tools:ignore="Autofill,LabelFor" />

        <Button
            android:id="@+id/button_send"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_alignParentBottom="true"
            android:enabled="false"
            android:gravity="bottom"
            android:text="@string/send"
            android:textAlignment="center" />
    </RelativeLayout>



</LinearLayout>