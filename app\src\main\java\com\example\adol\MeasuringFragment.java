package com.example.adol;

import android.content.Context;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.v4.app.Fragment;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;

import java.util.Objects;

import static com.example.adol.MainActivity.PROFILE_CONNECTED;


/**
 * A simple {@link Fragment} subclass.
 * Activities that contain this fragment must implement the
 * {@link MeasuringFragment.OnFragmentInteractionListener} interface
 * to handle interaction events.
 */
public class MeasuringFragment extends Fragment {

    private OnFragmentInteractionListener mListener;
    private Button button_connect;
    private Button button_start;
    private TextView textView_deviceName;
    private TextView textView_value_current;
    private TextView textView_value_SNR;
    private TextView textView_value_stdev;

    public MeasuringFragment() {
        // Required empty public constructor
    }


    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View view = inflater.inflate(R.layout.fragment_measuring, container, false);
        button_connect = view.findViewById(R.id.button_connect);
        button_start = view.findViewById(R.id.button_start);
        textView_deviceName = view.findViewById(R.id.textView_deviceName);
        textView_value_current = view.findViewById(R.id.textView_value_current);
        textView_value_SNR = view.findViewById(R.id.textView_value_SNR);
        textView_value_stdev = view.findViewById(R.id.textView_value_stdev);

        // Set the initial State depending on the current connection
        if(((MainActivity) Objects.requireNonNull(getActivity())).get_mState_connection() == PROFILE_CONNECTED){
            button_connect.setText(R.string.disconnect);
            button_start.setEnabled(true);
            String text = ((MainActivity)getActivity()).get_DeviceName()+ " - ready";
            textView_deviceName.setText(text);
            if(((MainActivity) getActivity()).get_mState_measuring()){
                button_start.setText(R.string.stop);
            }else{
                button_start.setText(R.string.start);
            }
        }else{
            button_connect.setText(R.string.connect);
            button_start.setEnabled(false);
            textView_deviceName.setText(R.string.not_connected);
        }


        button_connect.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                sendid(R.id.button_connect);
            }
        });

        button_start.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                button_start.setText(R.string.stop);
                sendid(R.id.button_start);
            }
        });
        return view;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        //you can set the title for your toolbar here for different fragments different titles
        Objects.requireNonNull(getActivity()).setTitle("Measuring");
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        if (context instanceof OnFragmentInteractionListener) {
            mListener = (OnFragmentInteractionListener) context;
        } else {
            throw new RuntimeException(context.toString()
                    + " must implement OnFragmentInteractionListener");
        }
    }

    @Override
    public void onDetach() {
        super.onDetach();
        mListener = null;
    }

    /**
     * Send the ID of the pressed Button to the MainActivity
     *
     * @param id of the pressed Button
     */

    private void sendid(int id) {
        if (mListener != null) {
            mListener.onFragmentInteraction(id);
        }
    }

    public interface OnFragmentInteractionListener {
        void onFragmentInteraction(int id);
    }

    /**
     * Change te text of e specific item
     *
     * @param text text to display
     * @param id XML id of the target item
     */

    public void changeText (String text, int id){
        switch (id){
            case R.id.button_connect:
                button_connect.setText(text);
                break;
            case R.id.button_start:
                button_start.setText(text);
                break;
            case R.id.textView_deviceName:
                textView_deviceName.setText(text);
                break;
            case R.id.textView_value_current:
                textView_value_current.setText(text);
                break;
            case R.id.textView_value_SNR:
                textView_value_SNR.setText(text);
                break;
            case R.id.textView_value_stdev:
                textView_value_stdev.setText(text);
                break;
        }
    }

    /**
     * Change status of a item to enable or disable
     *
     * @param true_false true enable, false disable
     * @param id XML id of the target item
     */
    public void setEnabledDisable (boolean true_false, int id){
        switch (id) {
            case R.id.button_start:
                button_start.setEnabled(true_false);
                break;
            case R.id.button_connect:
                button_connect.setEnabled(true_false);
                break;
        }
    }

}
