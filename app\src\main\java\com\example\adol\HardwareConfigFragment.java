package com.example.adol;



import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.v4.app.Fragment;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import java.util.Locale;
import java.util.Objects;


/**
 * A simple {@link Fragment} subclass.
 */
public class HardwareConfigFragment extends Fragment {

    public HardwareConfigFragment() {
        // Required empty public constructor
    }


    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }


    /**
     * Create the View and sets all data
     */
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View view = inflater.inflate(R.layout.fragment_hardwareconfig, container, false);

        TextView textView_value_TIAIntegrator = view.findViewById(R.id.textView_value_TIAIntegrator);
        TextView textView_value_gain = view.findViewById(R.id.textView_value_gain);
        TextView textView_value_VrefADC = view.findViewById(R.id.textView_value_VrefADC);
        TextView textView_value_GainADC_Photo = view.findViewById(R.id.textView_value_GainADC_Photo);
        TextView textView_value_GainADC_Bat = view.findViewById(R.id.textView_value_GainADC_Bat);
        TextView textView_value_GainADC_ref = view.findViewById(R.id.textView_value_GainADC_ref);
        TextView textView_value_numOfBit = view.findViewById(R.id.textView_value_numOfBit);
        TextView textView_value_capInt = view.findViewById(R.id.textView_value_capInt);
        TextView textView_value_dividerIntegrator = view.findViewById(R.id.textView_value_dividerIntegrator);
        TextView textView_value_dividerBat = view.findViewById(R.id.textView_value_dividerBat);


        if(ConfigHardware.TIA_Integrator != 0){
            textView_value_TIAIntegrator.setText(R.string.integrator);
            textView_value_capInt.setText(String.format(Locale.GERMAN,"%d pF",ConfigHardware.capacity_con));
        }else{
            textView_value_TIAIntegrator.setText(R.string.tia);
            textView_value_capInt.setText(R.string.not_used);
        }
        textView_value_gain.setText(String.format(Locale.GERMAN,"%1.0e V",(double)ConfigHardware.gain_TIA*1000));
        textView_value_VrefADC.setText(String.format(Locale.GERMAN,"%.2f V",ConfigHardware.Vref_ADC));
        textView_value_GainADC_Photo.setText(String.format(Locale.GERMAN,"%.3f",ConfigHardware.gain_ADC_Photo));
        textView_value_GainADC_Bat.setText(String.format(Locale.GERMAN,"%.3f",ConfigHardware.gain_ADC_Bat));
        textView_value_GainADC_ref.setText(String.format(Locale.GERMAN,"%.3f",ConfigHardware.gain_ADC_ref));
        textView_value_numOfBit.setText(String.format(Locale.GERMAN,"%d Bit",ConfigHardware.num_of_Bit_ADC));
        textView_value_dividerIntegrator.setText(String.format(Locale.GERMAN,"%.3f",ConfigHardware.divider_Integrator));
        textView_value_dividerBat.setText(String.format(Locale.GERMAN,"%.3f",ConfigHardware.divider_Bat));

        return view;
    }

    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        Objects.requireNonNull(getActivity()).setTitle("Hardware configuration");
    }


}
