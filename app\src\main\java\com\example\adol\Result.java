package com.example.adol;


/**
 * Class for storing and calculating the current and the voltages.
 */
class Result {
    
    double current;
    double snr;
    double standardDeviation;
    double vBat;
    double vRef;
    double currentOn;
    double currentOff;

    private static int index_std = 0;
    private static double[] currentArray = new double[50];


    Result (ReceivedValues value){

        if(ConfigHardware.TIA_Integrator == 0 ){
            current = (double)(value.ADCon - value.ADCoff) * ConfigHardware.factor_current * (double)(1000000 / ConfigHardware.gain_TIA);
            currentOn =(double)value.ADCon * ConfigHardware.factor_current * (double)(1000000 / ConfigHardware.gain_TIA);
            currentOff =(double)value.ADCoff * ConfigHardware.factor_current * (double)(1000000 / ConfigHardware.gain_TIA);
        }else{
            current = (double)(value.ADCon - value.ADCoff) * ConfigHardware.factor_current * ConfigHardware.divider_Integrator * (double)(ConfigHardware.capacity_con* 1000 / value.integrationtime);
            currentOn = (double)value.ADCon * ConfigHardware.factor_current * ConfigHardware.divider_Integrator * (double)(ConfigHardware.capacity_con* 1000 / value.integrationtime);
            currentOff = (double)value.ADCoff * ConfigHardware.factor_current * ConfigHardware.divider_Integrator * (double)(ConfigHardware.capacity_con* 1000 / value.integrationtime);
        }
        snr = (double) value.ADCon / value.ADCoff; // SNR

        currentArray[index_std] = current;
        index_std = (index_std+1)%currentArray.length;
        standardDeviation = calculateSD(currentArray);
        vBat = value.ADCBat*ConfigHardware.factor_Bat;
        vRef = value.ADCRef*ConfigHardware.factor_Vref;
    }

    /**
     * Calculates the standard deviation of the values in the Array
     *
     * @param numArray input number
     * @return standard deviation
     */
    private double calculateSD(double[] numArray)
    {
        double sum = 0.0;
        double standardDeviation = 0.0;
        int length = numArray.length;

        for(double num : numArray) {
            sum += num;
        }

        double mean = sum/length;

        for(double num: numArray) {
            standardDeviation += Math.pow(num - mean, 2);
        }

        return Math.sqrt(standardDeviation/length);
    }
}
