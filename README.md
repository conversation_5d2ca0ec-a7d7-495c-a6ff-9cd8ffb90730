# ADoL Android应用程序 - 蓝牙数据接收分析

## 概述

ADoL（芯片实验室安培检测）是一个Android应用程序，专为与蓝牙低功耗（BLE）传感器进行安培测量通信而设计。该应用程序无线接收传感器数据并处理它们以计算电流、信噪比（SNR）和标准偏差值。

## BluetoothGattCallback实现分析

### 核心回调类

应用程序在`ConnectionService`类中实现了自定义的`BluetoothGattCallback`来处理所有蓝牙GATT事件。此回调管理BLE通信的完整生命周期。

### 关键回调方法

#### 1. onConnectionStateChange()
```java
public void onConnectionStateChange(BluetoothGatt gatt, int status, int newState)
```

**功能**: 处理Android设备与BLE传感器之间的连接状态变化。

**处理流程**:
- **已连接状态**:
  - 设置连接状态为`STATE_CONNECTED`
  - 广播`ACTION_GATT_CONNECTED`意图
  - 通过`mBluetoothGatt.discoverServices()`自动启动服务发现
- **断开连接状态**:
  - 设置连接状态为`STATE_DISCONNECTED`
  - 重置就绪状态为`STATE_NOT_READY`
  - 广播`ACTION_GATT_DISCONNECTED`意图

#### 2. onServicesDiscovered()
```java
public void onServicesDiscovered(BluetoothGatt gatt, int status)
```

**功能**: 在连接的BLE设备上成功发现服务后调用。

**处理流程**:
- 验证发现成功（`GATT_SUCCESS`）
- 广播`ACTION_GATT_SERVICES_DISCOVERED`意图
- 触发数据特征的通知设置

#### 3. onCharacteristicRead()
```java
public void onCharacteristicRead(BluetoothGatt gatt, BluetoothGattCharacteristic characteristic, int status)
```

**功能**: 处理特征读取操作的响应。

**处理流程**:
- 检查读取操作是否成功
- 根据特征UUID路由数据:
  - `TX_CHAR_UUID`: 通用数据 → `ACTION_DATA_AVAILABLE`
  - `Data_CHAR`: 传感器数据 → `ACTION_SensorDATA_AVAILABLE`

#### 4. onCharacteristicChanged()
```java
public void onCharacteristicChanged(BluetoothGatt gatt, BluetoothGattCharacteristic characteristic)
```

**功能**: 当特征值发生变化时接收通知（主要数据接收方法）。

**处理流程**:
- 当传感器发送新数据时自动触发
- 根据特征UUID路由数据（与读取逻辑相同）
- 这是连续传感器数据接收的主要路径

#### 5. onDescriptorWrite()
```java
public void onDescriptorWrite(BluetoothGatt gatt, BluetoothGattDescriptor descriptor, int status)
```

**功能**: 使用队列系统管理通知启用过程。

**处理流程**:
- 处理通知设置队列（`queue_valueDescriptor`）
- 为队列中的下一个特征启用通知
- 当队列为空时，设置状态为`STATE_READY`并广播`ACTION_CONNECTION_READY`

## 详细的蓝牙数据接收过程

### 阶段1: 连接建立
1. **初始化**: `ConnectionService.initialize()`设置BluetoothManager和BluetoothAdapter
2. **连接**: `connect(address)`启动与目标设备的GATT连接
3. **状态变化**: `onConnectionStateChange()`确认连接成功
4. **服务发现**: 自动发现可用的GATT服务

### 阶段2: 服务配置
1. **服务发现完成**: 触发`onServicesDiscovered()`
2. **通知队列设置**: MainActivity将服务UUID添加到描述符队列:
   - `Data_Service` (UUID: 2C600004-FD64-4C2D-978F-8ACDAD001374)
   - `RX_SERVICE_UUID` (UUID: 2c600001-fd64-4c2d-978f-8acdad001374)
3. **通知启用**: 通过`enableNotification()`进行顺序处理

### 阶段3: 数据接收循环
1. **特征通知**: 传感器通过`onCharacteristicChanged()`发送数据
2. **数据路由**: 基于特征UUID:
   - **TX特征** (2c600003): 命令/响应数据
   - **数据特征** (2C600005): 传感器测量数据
3. **意图广播**: 数据打包在意图中并广播到MainActivity

### 阶段4: 数据处理
1. **意图接收**: MainActivity接收广播意图
2. **数据提取**: 从意图额外信息中提取原始字节数组
3. **对象创建**: 从字节数组创建`ReceivedValues`对象
4. **计算**: `Result`对象执行科学计算
5. **显示和存储**: 数据在UI中显示并保存到CSV文件

## 数据结构分析

### 原始数据格式 (ReceivedValues)
传感器传输13字节的数据，结构如下:

| 字节 | 字段 | 描述 |
|------|-------|-------------|
| 0 | ledNr | LED编号标识符 |
| 1-2 | ADCon | LED开启时的ADC值 (16位) |
| 3-4 | ADCoff | LED关闭时的ADC值 (16位) |
| 5-6 | integrationtime | 积分时间 (16位) |
| 7-8 | timeOffset | 测量间的时间偏移 (16位) |
| 9-10 | ADCBat | 电池电压ADC读数 (16位) |
| 11-12 | ADCRef | 参考电压ADC读数 (16位) |

### 处理后的数据 (Result)
应用程序计算:
- **电流**: ADCon和ADCoff之间的差值，转换为纳安培
- **信噪比**: 信噪比 (ADCon/ADCoff)
- **标准偏差**: 50个样本的滚动计算
- **电池电压**: 从ADC读数转换
- **参考电压**: 从ADC读数转换

## 自定义蓝牙服务

### 主要服务 (RX_SERVICE_UUID)
- **服务UUID**: `2c600001-fd64-4c2d-978f-8acdad001374`
- **RX特征**: `2c600002-fd64-4c2d-978f-8acdad001374` (写入)
- **TX特征**: `2c600003-fd64-4c2d-978f-8acdad001374` (通知)

### 数据服务 (Data_Service)
- **服务UUID**: `2C600004-FD64-4C2D-978F-8ACDAD001374`
- **数据特征**: `2C600005-FD64-4C2D-978F-8ACDAD001374` (通知)

### 客户端特征配置描述符 (CCCD)
- **UUID**: `00002902-0000-1000-8000-00805f9b34fb`
- **用途**: 启用/禁用特征的通知

## 架构组件

### ConnectionService
- 管理所有蓝牙GATT操作
- 实现BluetoothGattCallback
- 处理连接生命周期
- 通过LocalBroadcastManager提供数据广播

### MainActivity
- 从ConnectionService接收广播意图
- 处理原始传感器数据
- 管理UI更新和数据存储
- 协调各个片段之间的交互

### 数据类
- **ReceivedValues**: 解析来自传感器的原始字节数组
- **Result**: 对传感器数据执行科学计算
- **ConfigHardware**: 存储硬件配置参数

### UI组件
- **MeasuringFragment**: 实时数据显示
- **GraphFragment**: 数据可视化
- **CSV导出**: 数据记录功能

## 错误处理

应用程序包含全面的错误处理:
- 服务/特征可用性验证
- 连接状态监控
- 数据完整性检查
- 文件I/O错误管理
- 使用信号量的线程安全操作

## 数据流总结

```
BLE传感器 → onCharacteristicChanged() → broadcastUpdate() →
MainActivity → ReceivedValues → Result → UI显示 + CSV存储
```

这种架构确保了安培传感器数据的可靠、实时处理，具有适当的错误处理和数据持久性。
