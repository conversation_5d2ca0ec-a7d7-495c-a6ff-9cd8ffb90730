# ADoL Android Application - Bluetooth Data Reception Analysis

## Overview

ADoL (Amperometric Detection on Lab-on-chip) is an Android application designed to communicate with Bluetooth Low Energy (BLE) sensors for amperometric measurements. The application receives sensor data wirelessly and processes it to calculate current, signal-to-noise ratio (SNR), and standard deviation values.

## BluetoothGattCallback Implementation Analysis

### Core Callback Class

The application implements a custom `BluetoothGattCallback` in the `ConnectionService` class to handle all Bluetooth GATT events. This callback manages the complete lifecycle of BLE communication.

### Key Callback Methods

#### 1. onConnectionStateChange()
```java
public void onConnectionStateChange(BluetoothGatt gatt, int status, int newState)
```

**Purpose**: Handles connection state changes between the Android device and BLE sensor.

**Process Flow**:
- **Connected State**: 
  - Sets connection state to `STATE_CONNECTED`
  - Broadcasts `ACTION_GATT_CONNECTED` intent
  - Automatically initiates service discovery via `mBluetoothGatt.discoverServices()`
- **Disconnected State**:
  - Sets connection state to `STATE_DISCONNECTED` 
  - Resets ready state to `STATE_NOT_READY`
  - Broadcasts `ACTION_GATT_DISCONNECTED` intent

#### 2. onServicesDiscovered()
```java
public void onServicesDiscovered(BluetoothGatt gatt, int status)
```

**Purpose**: Called after successful service discovery on the connected BLE device.

**Process Flow**:
- Validates discovery success (`GATT_SUCCESS`)
- Broadcasts `ACTION_GATT_SERVICES_DISCOVERED` intent
- Triggers notification setup for data characteristics

#### 3. onCharacteristicRead()
```java
public void onCharacteristicRead(BluetoothGatt gatt, BluetoothGattCharacteristic characteristic, int status)
```

**Purpose**: Handles responses from characteristic read operations.

**Process Flow**:
- Checks for successful read operation
- Routes data based on characteristic UUID:
  - `TX_CHAR_UUID`: General data → `ACTION_DATA_AVAILABLE`
  - `Data_CHAR`: Sensor data → `ACTION_SensorDATA_AVAILABLE`

#### 4. onCharacteristicChanged()
```java
public void onCharacteristicChanged(BluetoothGatt gatt, BluetoothGattCharacteristic characteristic)
```

**Purpose**: Receives notifications when characteristic values change (primary data reception method).

**Process Flow**:
- Automatically triggered when sensor sends new data
- Routes data based on characteristic UUID (same logic as read)
- This is the main pathway for continuous sensor data reception

#### 5. onDescriptorWrite()
```java
public void onDescriptorWrite(BluetoothGatt gatt, BluetoothGattDescriptor descriptor, int status)
```

**Purpose**: Manages notification enablement process using a queue system.

**Process Flow**:
- Processes notification setup queue (`queue_valueDescriptor`)
- Enables notifications for next characteristic in queue
- When queue is empty, sets state to `STATE_READY` and broadcasts `ACTION_CONNECTION_READY`

## Detailed Bluetooth Data Reception Process

### Phase 1: Connection Establishment
1. **Initialization**: `ConnectionService.initialize()` sets up BluetoothManager and BluetoothAdapter
2. **Connection**: `connect(address)` initiates GATT connection with target device
3. **State Change**: `onConnectionStateChange()` confirms connection success
4. **Service Discovery**: Automatic discovery of available GATT services

### Phase 2: Service Configuration
1. **Service Discovery Complete**: `onServicesDiscovered()` triggered
2. **Notification Queue Setup**: MainActivity adds service UUIDs to descriptor queue:
   - `Data_Service` (UUID: 2C600004-FD64-4C2D-978F-8ACDAD001374)
   - `RX_SERVICE_UUID` (UUID: 2c600001-fd64-4c2d-978f-8acdad001374)
3. **Notification Enablement**: Sequential processing via `enableNotification()`

### Phase 3: Data Reception Loop
1. **Characteristic Notification**: Sensor sends data via `onCharacteristicChanged()`
2. **Data Routing**: Based on characteristic UUID:
   - **TX Characteristic** (2c600003): Command/response data
   - **Data Characteristic** (2C600005): Sensor measurement data
3. **Intent Broadcasting**: Data packaged in intents and broadcast to MainActivity

### Phase 4: Data Processing
1. **Intent Reception**: MainActivity receives broadcast intents
2. **Data Extraction**: Raw byte arrays extracted from intent extras
3. **Object Creation**: `ReceivedValues` object created from byte array
4. **Calculation**: `Result` object performs scientific calculations
5. **Display & Storage**: Data shown in UI and saved to CSV file

## Data Structure Analysis

### Raw Data Format (ReceivedValues)
The sensor transmits 13 bytes of data structured as follows:

| Byte | Field | Description |
|------|-------|-------------|
| 0 | ledNr | LED number identifier |
| 1-2 | ADCon | ADC value with LED on (16-bit) |
| 3-4 | ADCoff | ADC value with LED off (16-bit) |
| 5-6 | integrationtime | Integration time (16-bit) |
| 7-8 | timeOffset | Time offset between measurements (16-bit) |
| 9-10 | ADCBat | Battery voltage ADC reading (16-bit) |
| 11-12 | ADCRef | Reference voltage ADC reading (16-bit) |

### Processed Data (Result)
The application calculates:
- **Current**: Difference between ADCon and ADCoff, converted to nanoamperes
- **SNR**: Signal-to-noise ratio (ADCon/ADCoff)
- **Standard Deviation**: Rolling calculation over 50 samples
- **Battery Voltage**: Converted from ADC reading
- **Reference Voltage**: Converted from ADC reading

## Custom Bluetooth Services

### Primary Service (RX_SERVICE_UUID)
- **Service UUID**: `2c600001-fd64-4c2d-978f-8acdad001374`
- **RX Characteristic**: `2c600002-fd64-4c2d-978f-8acdad001374` (Write)
- **TX Characteristic**: `2c600003-fd64-4c2d-978f-8acdad001374` (Notify)

### Data Service (Data_Service)
- **Service UUID**: `2C600004-FD64-4C2D-978F-8ACDAD001374`
- **Data Characteristic**: `2C600005-FD64-4C2D-978F-8ACDAD001374` (Notify)

### Client Characteristic Configuration Descriptor (CCCD)
- **UUID**: `00002902-0000-1000-8000-00805f9b34fb`
- **Purpose**: Enables/disables notifications for characteristics

## Architecture Components

### ConnectionService
- Manages all Bluetooth GATT operations
- Implements BluetoothGattCallback
- Handles connection lifecycle
- Provides data broadcasting via LocalBroadcastManager

### MainActivity
- Receives broadcast intents from ConnectionService
- Processes raw sensor data
- Manages UI updates and data storage
- Coordinates between fragments

### Data Classes
- **ReceivedValues**: Parses raw byte arrays from sensor
- **Result**: Performs scientific calculations on sensor data
- **ConfigHardware**: Stores hardware configuration parameters

### UI Components
- **MeasuringFragment**: Real-time data display
- **GraphFragment**: Data visualization
- **CSV Export**: Data logging functionality

## Error Handling

The application includes comprehensive error handling:
- Service/characteristic availability validation
- Connection state monitoring
- Data integrity checks
- File I/O error management
- Thread-safe operations using semaphores

## Data Flow Summary

```
BLE Sensor → onCharacteristicChanged() → broadcastUpdate() → 
MainActivity → ReceivedValues → Result → UI Display + CSV Storage
```

This architecture ensures reliable, real-time processing of amperometric sensor data with proper error handling and data persistence.
