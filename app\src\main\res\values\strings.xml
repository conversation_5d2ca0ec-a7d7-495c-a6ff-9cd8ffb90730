<resources>
    <string name="app_name">ADoL</string>
    <string name="navigation_drawer_open">Open navigation drawer</string>
    <string name="navigation_drawer_close">Close navigation drawer</string>
    <string name="nav_header_title">Automatic Dtection of Lymphedema</string>
    <string name="nav_header_subtitle">BA19_loma_2</string>
    <string name="nav_header_desc">Navigation header</string>

    <string name="start">Start</string>
    <string name="connect">Connect</string>
    <string name="disconnect">Disconnect</string>
    <string name="stop">Stop</string>
    <string name="current">Current</string>
    <string name="snr">SNR</string>
    <string name="stdev">Standard Deviation</string>


    <string name="measuring_time">Measuring Time</string>
    <string name="led_nr">LED Nr.</string>
    <string name="send">Send</string>
    <string name="time_offset">Time offset</string>

    <string name="select_device">Select a device</string>
    <string name="scanning">Scanning for devices…</string>
    <string name="ble_not_supported">Bluetooth Low Energy not supported</string>
    <string name="scan">Scan</string>
    <string name="cancel">Cancel</string>
    <string name="paired">paired</string>
    <string name="device">Device :</string>
    <string name="select_a_device">Select a device</string>
    <string name="tiaintegrator">TIA / Integrator</string>
    <string name="gain_tia">Gain TIA</string>
    <string name="vref_adc">Vref ADC</string>
    <string name="resolution">Resolution</string>
    <string name="integratorcapacity">Integratorcapacity</string>
    <string name="divider_integrator">Divider Integrator</string>
    <string name="divider_battery">Divider Battery</string>
    <string name="path_of_file">Path of File</string>
    <string name="gain_adc_photo">Gain ADC Photo</string>
    <string name="gain_adc_battery">Gain ADC Battery</string>
    <string name="gain_adc_reference">Gain ADC Reference</string>
    <string name="save_file">Save File</string>
    <string name="text_connecting">- connecting</string>
    <string name="integrator">Integrator</string>
    <string name="tia">TIA</string>
    <string name="not_connected">Not Connected</string>
    <string name="v_reference">V reference</string>
    <string name="v_battery">V battery</string>
    <string name="not_used">not used</string>
    <string name="current_on">Current on</string>
    <string name="current_off">Current off</string>
    <string name="mean_current_over_all_led_s">Mean current over all LED\'s</string>
    <string name="measuring">Measuring</string>
    <string name="scientific_mode">Scientific mode</string>
    <string name="debugging">Debugging</string>
    <string name="hardware_config">Hardware config</string>
    <string name="plotting">Plotting</string>
    <string name="graph">Graph</string>


</resources>
