<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MeasuringFragment">

    <Button
        android:id="@+id/button_connect"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="false"
        android:layout_marginStart="10dp"
        android:layout_marginTop="30dp"
        android:layout_marginEnd="10dp"
        android:soundEffectsEnabled="true"
        android:text="@string/connect"
        android:textAppearance="?android:attr/textAppearanceMedium" />

    <Button
        android:id="@+id/button_start"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/button_connect"
        android:layout_alignParentEnd="false"
        android:layout_marginStart="10dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="10dp"
        android:enabled="false"
        android:soundEffectsEnabled="true"
        android:text="@string/start"
        android:textAppearance="?android:attr/textAppearanceMedium" />

    <TextView
        android:id="@+id/textView_current"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/button_start"
        android:layout_marginStart="10dp"
        android:layout_marginTop="10dp"
        android:textAppearance="?android:attr/textAppearanceMedium"
        android:text="@string/current" />


    <TextView
        android:id="@+id/textView_SNR"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/textView_current"
        android:layout_alignStart="@id/textView_current"
        android:layout_marginTop="10dp"
        android:textAppearance="?android:attr/textAppearanceMedium"
        android:text="@string/snr" />

    <TextView
        android:id="@+id/textView_stdev"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/textView_SNR"
        android:layout_alignStart="@id/textView_current"
        android:layout_marginTop="10dp"
        android:textAppearance="?android:attr/textAppearanceMedium"
        android:text="@string/stdev" />

    <TextView
        android:id="@+id/textView_value_current"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_alignTop="@+id/textView_current"
        android:layout_marginEnd="10dp"
        android:gravity="end"
        android:layout_toEndOf="@id/textView_current"
        android:textAppearance="?android:attr/textAppearanceMedium"
        android:text=""/>

    <TextView
        android:id="@+id/textView_value_SNR"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@+id/textView_SNR"
        android:layout_alignEnd="@+id/textView_value_current"
        android:gravity="end"
        android:layout_toEndOf="@id/textView_SNR"
        android:textAppearance="?android:attr/textAppearanceMedium"
        android:text="" />

    <TextView
        android:id="@+id/textView_value_stdev"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@+id/textView_stdev"
        android:layout_alignEnd="@+id/textView_value_SNR"
        android:gravity="end"
        android:textAppearance="?android:attr/textAppearanceMedium"
        android:layout_toEndOf="@id/textView_stdev"
        android:text="" />

    <TextView
        android:id="@+id/textView_deviceName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="false"
        android:layout_alignParentBottom="true"
        android:layout_marginStart="5dp"
        android:layout_toEndOf="@id/textView_deviceLabel"
        android:text="@string/select_a_device" />

    <TextView
        android:id="@+id/textView_deviceLabel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:text="@string/device" />

</RelativeLayout>