package com.example.adol;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.ServiceConnection;
import android.os.Bundle;
import android.os.Environment;
import android.os.IBinder;
import android.support.annotation.NonNull;
import android.support.design.widget.NavigationView;
import android.support.v4.app.Fragment;
import android.support.v4.app.FragmentTransaction;
import android.support.v4.content.LocalBroadcastManager;
import android.support.v4.view.GravityCompat;
import android.support.v4.widget.DrawerLayout;
import android.support.v7.app.ActionBarDrawerToggle;
import android.support.v7.app.AlertDialog;
import android.support.v7.app.AppCompatActivity;
import android.support.v7.widget.Toolbar;
import android.util.Log;
import android.view.Menu;
import android.view.MenuItem;
import android.widget.TextView;
import android.widget.Toast;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.concurrent.Semaphore;

/**
 * MainActivity of the ADol (Automatic Detection of Lymphedema)
 *
 * <AUTHOR> Ludwig
 */

public class MainActivity extends AppCompatActivity
        implements NavigationView.OnNavigationItemSelectedListener, DebuggingFragment.OnFragmentInteractionListener, MeasuringFragment.OnFragmentInteractionListener {

    public static final int PROFILE_CONNECTED = 20;
    private static final String TAG = "ADoL";
    private static final int REQUEST_SELECT_DEVICE = 1;
    private static final int REQUEST_ENABLE_BT = 2;
    private static final int PROFILE_DISCONNECTED = 21;
    private static final int STATE_NOT_READY = 3;
    private static final int STATE_READY = 4;

    /** for safe File handling */
    public static Semaphore semaphore = new Semaphore(1);

    // Initialise all Fragments
    private MeasuringFragment measuringFragment = new MeasuringFragment();
    private DebuggingFragment debuggingFragment = new DebuggingFragment();
    private HardwareConfigFragment hardwareConfigFragment = new HardwareConfigFragment();
    private GraphFragment graphFragment = new GraphFragment();
    // Bluetooth
    private ConnectionService mService = null;
    private BluetoothDevice mDevice = null;
    private BluetoothAdapter mBtAdapter = null;
    private int mState_connection = PROFILE_DISCONNECTED;
    private int mState_ready = STATE_NOT_READY;


    // Is the hardware measuring
    private boolean mState_measuring = false;

    // Initialise the File for storing the data
    private File SensorDatafile = null;
    private final BroadcastReceiver ADoLStatusChangeReceiver = new BroadcastReceiver() {
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();

            //*********************//
            if (action != null && action.equals(ConnectionService.ACTION_GATT_CONNECTED)) {
                runOnUiThread(new Runnable() {
                    public void run() {
                        Log.d(TAG, "ADoL_CONNECT_MSG");
                        mState_connection = PROFILE_CONNECTED;
                        measuringFragment.changeText("Connecting", R.id.button_connect);
                        measuringFragment.setEnabledDisable(false, R.id.button_connect);

                    }
                });
            }
            //*********************//
            if (action != null && action.equals(ConnectionService.ACTION_GATT_DISCONNECTED)) {
                runOnUiThread(new Runnable() {
                    public void run() {
                        Log.d(TAG, "ADoL_DISCONNECT_MSG");
                        mState_connection = PROFILE_DISCONNECTED;
                        mState_ready = STATE_NOT_READY;
                        display_text_and_enableDisable();
                        mService.close();
                        //setUiState();

                    }
                });
            }
            //*********************//
            if (action != null && action.equals(ConnectionService.ACTION_GATT_SERVICES_DISCOVERED)) {
                mService.addtoDescriptorQueue(ConnectionService.Data_Service);
                mService.addtoDescriptorQueue(ConnectionService.RX_SERVICE_UUID);
                mService.startwriteDescriptor();
            }
            //*********************//
            if (action != null && action.equals(ConnectionService.ACTION_CONNECTION_READY)) {
                mState_ready = STATE_READY;
                display_text_and_enableDisable();
            }
            //*********************//
            if (action != null && action.equals(ConnectionService.ACTION_DATA_AVAILABLE)) {
                final byte[] txValue = intent.getByteArrayExtra(ConnectionService.EXTRA_DATA);
                runOnUiThread(new Runnable() {
                    public void run() {
                        try {
                            if (txValue[0] == 0x43) {
                                new ConfigHardware(txValue);
                            }
                        } catch (Exception e) {
                            Log.e(TAG, e.toString());
                        }
                    }
                });
            }
            //*********************//
            if (action != null && action.equals(ConnectionService.ACTION_SensorDATA_AVAILABLE)) {
                final byte[] txValue = intent.getByteArrayExtra(ConnectionService.SENSOR_DATA);
                runOnUiThread(new Runnable() {
                    public void run() {
                        try {
                            ReceivedValues value = new ReceivedValues(txValue);
                            calculate_Data(value);
                        } catch (Exception e) {
                            Log.e(TAG, e.toString());

                        }
                    }
                });
            }
            //*********************//
            if (action != null && action.equals(ConnectionService.DEVICE_DOES_NOT_SUPPORT_ADol)) {
                showMessage("Device doesn't support ADoL. Disconnecting");
                //textView_deviceName.setText("Not Connected");
                display_text_and_enableDisable();
                mService.disconnect();
            }
        }
    };
    private ServiceConnection mServiceConnection = new ServiceConnection() {
        public void onServiceConnected(ComponentName className, IBinder rawBinder) {
            mService = ((ConnectionService.LocalBinder) rawBinder).getService();
            Log.d(TAG, "onServiceConnected mService= " + mService);
            if (!mService.initialize()) {
                Log.e(TAG, "Unable to initialize Bluetooth");
                finish();
            }

        }

        public void onServiceDisconnected(ComponentName classname) {
            //     mService.disconnect(mDevice);
            mService = null;
        }
    };

    private static IntentFilter makeGattUpdateIntentFilter() {
        final IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(ConnectionService.ACTION_GATT_CONNECTED);
        intentFilter.addAction(ConnectionService.ACTION_GATT_DISCONNECTED);
        intentFilter.addAction(ConnectionService.ACTION_GATT_SERVICES_DISCOVERED);
        intentFilter.addAction(ConnectionService.ACTION_CONNECTION_READY);
        intentFilter.addAction(ConnectionService.ACTION_DATA_AVAILABLE);
        intentFilter.addAction(ConnectionService.ACTION_SensorDATA_AVAILABLE);
        intentFilter.addAction(ConnectionService.DEVICE_DOES_NOT_SUPPORT_ADol);
        return intentFilter;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setContentView(R.layout.activity_main);
        mBtAdapter = BluetoothAdapter.getDefaultAdapter();
        if (mBtAdapter == null) {
            Toast.makeText(this, "Bluetooth is not available", Toast.LENGTH_LONG).show();
            finish();
            return;
        }
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);

        DrawerLayout drawer = findViewById(R.id.drawer_layout);
        ActionBarDrawerToggle toggle = new ActionBarDrawerToggle(
                this, drawer, toolbar, R.string.navigation_drawer_open, R.string.navigation_drawer_close);
        drawer.addDrawerListener(toggle);
        toggle.syncState();

        NavigationView navigationView = findViewById(R.id.nav_view);
        navigationView.setNavigationItemSelectedListener(this);
        // Initialise the connection Service
        service_init();

        if (savedInstanceState != null) {
            return;
        }

        // Start screen
        displaySelectedScreen(R.id.nav_Messung);
    }

    @Override
    public void onBackPressed() {
        DrawerLayout drawer = findViewById(R.id.drawer_layout);
        if (drawer.isDrawerOpen(GravityCompat.START)) {
            drawer.closeDrawer(GravityCompat.START);
        } else {
            super.onBackPressed();
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        // Inflate the menu; this adds items to the action bar if it is present.
        getMenuInflater().inflate(R.menu.main, menu);
        return true;
    }

    @Override
    public boolean onNavigationItemSelected(@NonNull MenuItem item) {
        // Handle navigation view item clicks here.
        //calling the method displaySelectedScreen and passing the id of selected menu
        displaySelectedScreen(item.getItemId());

        return true;
    }

    @Override
    public void onStart() {
        super.onStart();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "onDestroy()");

        try {
            LocalBroadcastManager.getInstance(this).unregisterReceiver(ADoLStatusChangeReceiver);
        } catch (Exception e) {
            Log.e(TAG, e.toString());
        }
        try {
            unbindService(mServiceConnection);
            mService.stopSelf();
            mService = null;
        } catch (Exception e) {
            Log.e(TAG, e.toString());
        }

    }

    @Override
    protected void onStop() {
        Log.d(TAG, "onStop");
        super.onStop();
    }

    @Override
    protected void onPause() {
        Log.d(TAG, "onPause");
        super.onPause();
    }

    @Override
    protected void onRestart() {
        super.onRestart();
        Log.d(TAG, "onRestart");
    }

    @Override
    public void onResume() {
        super.onResume();
        Log.d(TAG, "onResume");
        if (!mBtAdapter.isEnabled()) {
            Log.i(TAG, "onResume - BT not enabled yet");
            Intent enableIntent = new Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE);
            startActivityForResult(enableIntent, REQUEST_ENABLE_BT);
        }

    }

    private void service_init() {
        Intent bindIntent = new Intent(this, ConnectionService.class);
        bindService(bindIntent, mServiceConnection, Context.BIND_AUTO_CREATE);
        LocalBroadcastManager.getInstance(this).registerReceiver(ADoLStatusChangeReceiver, makeGattUpdateIntentFilter());
    }

    /**
     * Changes the Fragment
     *
     * @param itemId ID of pressed Item
     */
    private void displaySelectedScreen(int itemId) {
        //creating fragment object
        Fragment fragment = null;
        String tag = "";


        //initializing the fragment object which is selected
        switch (itemId) {
            case R.id.nav_Messung:
                fragment = measuringFragment;
                tag = "measuringFragment";
                break;
            case R.id.nav_debugging:
                fragment = debuggingFragment;
                tag = "debuggingFragment";
                break;
            case R.id.nav_tools:
                fragment = hardwareConfigFragment;
                tag = "hardwareConfigFragment";
                break;
            case R.id.nav_graph:
                fragment = graphFragment;
                tag = "graphFragment";
                break;
        }

        //replacing the fragment
        if (fragment != null) {
            FragmentTransaction ft = getSupportFragmentManager().beginTransaction();
            ft.replace(R.id.content_frame, fragment, tag);
            ft.commit();
        }
        DrawerLayout drawer = findViewById(R.id.drawer_layout);
        drawer.closeDrawer(GravityCompat.START);
    }

    /**
     * Interface of {@link DebuggingFragment}
     *
     * @param command byte Array to send
     * @param id      xml ID of pressed Button
     */
    @Override
    public void onFragmentInteraction(byte[] command, int id) {
        if (id == R.id.button_saveFile) {
            AlertDialog.Builder alertDialogBuilder = new AlertDialog.Builder(this);
            alertDialogBuilder.setMessage("Would you like to save the File");
            alertDialogBuilder.setPositiveButton("YES", new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface arg0, int arg1) {
                    @SuppressLint("SimpleDateFormat") SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd-HH-mm-ss");
                    String currentDateTimeString = format.format(new Date());
                    File old_SensorDatafile = new File(Environment.getExternalStorageDirectory() + "/Sensor_Data/sensor_data.csv");
                    File new_SensorDatafile = new File(Environment.getExternalStorageDirectory() + "/Sensor_Data/" + currentDateTimeString + "_sensor_data.csv");

                    InputStream is = null;
                    OutputStream os = null;
                    try {
                        is = new FileInputStream(old_SensorDatafile);
                        os = new FileOutputStream(new_SensorDatafile);
                        byte[] buffer = new byte[1024];
                        int length;
                        while ((length = is.read(buffer)) > 0) {
                            os.write(buffer, 0, length);
                        }
                    } catch (Exception e) {
                        Log.e(TAG, e.toString());
                    } finally {
                        try {
                            assert is != null;
                            assert os != null;
                            is.close();
                            os.close();
                            //noinspection ResultOfMethodCallIgnored
                            old_SensorDatafile.delete();
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
            });

            alertDialogBuilder.setNegativeButton("NO", null);
            AlertDialog alertDialog = alertDialogBuilder.create();
            alertDialog.show();
            graphFragment.resetDataInGraph();
        } else {
            if (command[0] == 0x41) { // Start
                mState_measuring = true;
            } else if (command[0] == 0x42) { // Stop
                mState_measuring = false;
            }
            mService.writeRXCharacteristic(command);
        }
    }

    /**
     * Interface of {@link MeasuringFragment}
     *
     * @param id xml ID of pressed Button
     */
    @Override
    public void onFragmentInteraction(int id) {
        switch (id) {
            case R.id.button_connect:
                if (!mBtAdapter.isEnabled()) {
                    Log.i(TAG, "onClick - BT not enabled yet");
                    Intent enableIntent = new Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE);
                    startActivityForResult(enableIntent, REQUEST_ENABLE_BT);
                } else {
                    if (mState_connection != PROFILE_CONNECTED) {//button_connect.getText().equals("Connect")
                        //Connect button pressed, open DeviceListActivity class, with popup windows that scan for devices

                        Intent newIntent = new Intent(MainActivity.this, DeviceListActivity.class);
                        startActivityForResult(newIntent, REQUEST_SELECT_DEVICE);
                    } else {
                        //Disconnect button pressed
                        if (mDevice != null) {
                            mService.disconnect();
                        }
                    }
                }
                break;
            case R.id.button_start:
                if (mState_connection == PROFILE_CONNECTED) {
                    if (!mState_measuring) {
                        byte[] value = new byte[]{(byte) 0x41};
                        //send data to service
                        mService.writeRXCharacteristic(value);
                        mState_measuring = true;
                    } else {
                        byte[] value = new byte[]{(byte) 0x42};
                        //send data to service
                        mService.writeRXCharacteristic(value);
                        mState_measuring = false;
                    }
                    display_text_and_enableDisable();
                } else {
                    showMessage("Not connected yet");
                }
                break;
        }

    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        switch (requestCode) {
            case REQUEST_SELECT_DEVICE:
                //When the DeviceListActivity return, with the selected device address
                if (resultCode == Activity.RESULT_OK && data != null) {
                    String deviceAddress = data.getStringExtra(BluetoothDevice.EXTRA_DEVICE);
                    mDevice = BluetoothAdapter.getDefaultAdapter().getRemoteDevice(deviceAddress);

                    Log.d(TAG, "... onActivityResultdevice.address==" + mDevice + "mserviceValue" + mService);
                    String text = mDevice.getName() + getString(R.string.text_connecting);
                    ((TextView) this.findViewById(R.id.textView_deviceName)).setText(text);
                    mService.connect(deviceAddress);
                }
                break;
            case REQUEST_ENABLE_BT:
                // When the request to enable Bluetooth returns
                if (resultCode == Activity.RESULT_OK) {
                    Toast.makeText(this, "Bluetooth has turned on ", Toast.LENGTH_SHORT).show();

                } else {
                    // User did not enable Bluetooth or an error occurred
                    Log.d(TAG, "BT not enabled");
                    showMessage("Problem in BT turning ON ");
                    finish();
                }
                break;
            default:
                Log.e(TAG, "wrong request code");
                break;
        }
    }

    /**
     * Calculates the current, SNR and standard deviation from the raw data
     *
     * @param value raw data from the sensor
     */

    private void calculate_Data(ReceivedValues value) {

        if (mState_measuring) {
            Result result = new Result(value);
            show_Data(value, result);
            writeCsvData(value, result);
        }
    }

    /**
     * Storing the Data in to a csv File on the Phone
     *
     * @param value  raw data from sensor
     * @param result calculated data
     */

    private void writeCsvData(ReceivedValues value, Result result) {
        @SuppressLint("SimpleDateFormat") SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        String currentDateMilSec = Long.toString(new Date().getTime());
        String currentDateTimeString = format.format(new Date());
        String line = String.format(Locale.ENGLISH, currentDateTimeString + ";" + currentDateMilSec + ";%f;%f;%f;%f;%f;%d;%d;%d;%d;%d;%d;%d\n", result.current, result.snr, result.standardDeviation, result.vBat, result.vRef,
                value.ledNr, value.ADCon, value.ADCoff, value.integrationtime, value.timeOffset, value.ADCBat, value.ADCRef);

        try {
            semaphore.acquire();
            // Stores the values
            File folder = new File(Environment.getExternalStorageDirectory() + "/Sensor_Data");
            if (!folder.exists()) {
                if (!folder.mkdir()) {
                    throw new FileNotFoundException("could not create folder");
                }
            }
            SensorDatafile = new File(folder + "/" + "sensor_data.csv");
            boolean file_exist = SensorDatafile.exists();
            FileOutputStream fos = new FileOutputStream(SensorDatafile, true);
            if (!file_exist) {
                String line_info = "Date;Date in ms;Current;SNR;Standard deviation;Battery voltage;Reference Voltage;LED Nr.;ADC ON;ADC OFF;Integration Time;Time between;ADC Battery;ADC Reference\n"; // Übertitel im CSV File
                fos.write(line_info.getBytes());
            }
            fos.write(line.getBytes());
            fos.close();
        } catch (FileNotFoundException e) {
            Log.e(TAG, "File not found");
        } catch (Exception e) {
            Log.e(TAG, "writing to file not possible");
        } finally {
            semaphore.release();
        }
    }

    /**
     * Displays the data on the active Fragment
     *
     * @param value  raw data from sensor
     * @param result calculated data
     */

    private void show_Data(ReceivedValues value, Result result) {

        Fragment meFragment = getSupportFragmentManager().findFragmentByTag("measuringFragment");
        if (meFragment != null && meFragment.isVisible()) {
            measuringFragment.changeText(String.format(Locale.GERMAN, "%.3f nA", result.current), R.id.textView_value_current);
            measuringFragment.changeText(String.format(Locale.GERMAN, "%.3f", result.snr), R.id.textView_value_SNR);
            measuringFragment.changeText(String.format(Locale.GERMAN, "%.3f nA", result.standardDeviation), R.id.textView_value_stdev);
        }
        DebuggingFragment deFragment = (DebuggingFragment) getSupportFragmentManager().findFragmentByTag("debuggingFragment");
        if (deFragment != null && deFragment.isVisible()) {
            debuggingFragment.changeText(String.format(Locale.GERMAN, "%.3f nA", result.current), R.id.textView_value_current);
            debuggingFragment.changeText(String.format(Locale.GERMAN, "%.3f", result.snr), R.id.textView_value_SNR);
            debuggingFragment.changeText(String.format(Locale.GERMAN, "%.3f nA", result.standardDeviation), R.id.textView_value_stdev);
            debuggingFragment.changeText(String.format(Locale.GERMAN, "%d", value.ledNr), R.id.textView_value_LEDNr);
            debuggingFragment.changeText(String.format(Locale.GERMAN, "%d us", value.integrationtime), R.id.textView_value_mesTime);
            debuggingFragment.changeText(String.format(Locale.GERMAN, "%.3f nA", result.currentOn), R.id.textView_value_ADCon);
            debuggingFragment.changeText(String.format(Locale.GERMAN, "%.3f nA", result.currentOff), R.id.textView_value_ADCoff);
            debuggingFragment.changeText(String.format(Locale.GERMAN, "%d ms", value.timeOffset), R.id.textView_value_Timeoffset);
            debuggingFragment.changeText(String.format(Locale.GERMAN, "%.3f V", result.vBat), R.id.textView_value_ADCbatt);
            debuggingFragment.changeText(String.format(Locale.GERMAN, "%.3f V", result.vRef), R.id.textView_value_ADCref);
        }

    }

    private void showMessage(String msg) {
        Toast.makeText(this, msg, Toast.LENGTH_SHORT).show();
    }

    public int get_mState_connection() {
        return mState_connection;
    }

    public boolean get_mState_measuring() {
        return mState_measuring;
    }

    public String get_DeviceName() {
        return mDevice.getName();
    }

    public String get_FilePath() {
        File file = new File(Environment.getExternalStorageDirectory() + "/Sensor_Data/sensor_data.csv");
        if (file.exists()) {
            return file.getPath();
        } else {
            return "File does not exist";
        }
    }

    private void display_text_and_enableDisable() {
        Fragment meFragment = getSupportFragmentManager().findFragmentByTag("measuringFragment");
        if (meFragment != null && meFragment.isVisible()) {
            if (mState_ready == STATE_READY) {
                measuringFragment.changeText("Disconnect", R.id.button_connect);
                measuringFragment.setEnabledDisable(true, R.id.button_connect);
                measuringFragment.setEnabledDisable(true, R.id.button_start);
                measuringFragment.changeText(mDevice.getName() + " - ready", R.id.textView_deviceName);
                if (mState_measuring) {
                    measuringFragment.changeText("Stop", R.id.button_start);
                } else {
                    measuringFragment.changeText("Start", R.id.button_start);
                }

            } else {
                measuringFragment.changeText("Connect", R.id.button_connect);
                measuringFragment.setEnabledDisable(true, R.id.button_connect);
                measuringFragment.setEnabledDisable(false, R.id.button_start);
                measuringFragment.changeText("Not Connected", R.id.textView_deviceName);
            }
        }
        DebuggingFragment deFragment = (DebuggingFragment) getSupportFragmentManager().findFragmentByTag("debuggingFragment");
        if (deFragment != null && deFragment.isVisible()) {
            if (mState_ready == STATE_READY) {
                debuggingFragment.setEnabledDisable(true, R.id.button_send);
                debuggingFragment.setEnabledDisable(true, R.id.text_command);
            } else {
                debuggingFragment.setEnabledDisable(false, R.id.button_send);
                debuggingFragment.setEnabledDisable(false, R.id.text_command);
            }
        }
    }
}
